{"version": 3, "file": "rawRequest.d.ts", "sourceRoot": "", "sources": ["../../../src/legacy/functions/rawRequest.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,qBAAqB,EACrB,iBAAiB,EACjB,SAAS,EACT,8BAA8B,EAC/B,MAAM,qBAAqB,CAAA;AAE5B;;GAEG;AACH,eAAO,MAAM,UAAU,EAAE,UASxB,CAAA;AAGD,UAAU,UAAU;IAClB,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,0BAA0B,EAAE,8BAA8B,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAA;IACrK,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,OAAO,EAAE,yBAAyB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAA;CAC/G;AAOD,eAAO,MAAM,2BAA2B,GAAI,CAAC,SAAS,SAAS,yBAC/C,MAAM,GAAG,yBAAyB,CAAC,CAAC,CAAC,UAC3C,MAAM,iCACiB,8BAA8B,CAAC,CAAC,CAAC,KAC/D,yBAAyB,CAAC,CAAC,CAW7B,CAAA;AAED,MAAM,MAAM,yBAAyB,CAAC,CAAC,SAAS,SAAS,GAAG,SAAS,IAAI;IACvE,GAAG,EAAE,MAAM,CAAA;CACZ,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAA;AAExB,eAAO,MAAM,mBAAmB,GAAI,CAAC,SAAS,SAAS,2BACrC,MAAM,GAAG,iBAAiB,CAAC,CAAC,CAAC,cACjC,CAAC,mBACI,WAAW,KAC3B,iBAAiB,CAAC,CAAC,CASrB,CAAA"}