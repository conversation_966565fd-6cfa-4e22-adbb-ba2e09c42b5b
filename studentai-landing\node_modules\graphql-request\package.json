{"name": "graphql-request", "version": "7.2.0", "type": "module", "main": "./build/entrypoints/main.js", "exports": {".": {"default": "./build/entrypoints/main.js"}}, "files": ["build", "src"], "repository": {"type": "git", "url": "https://github.com/jasonkuhrt/graphql-request.git"}, "keywords": ["graphql", "request", "fetch", "graphql-client"], "author": {"name": "<PERSON>", "email": "jason<PERSON><PERSON><EMAIL>", "url": "https://kuhrt.me"}, "license": "MIT", "bugs": {"url": "https://github.com/jasonkuhrt/graphql-request/issues"}, "homepage": "https://github.com/jasonkuhrt/graphql-request", "dependencies": {"@graphql-typed-document-node/core": "^3.2.0"}, "peerDependencies": {"graphql": "14 - 16"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.3", "@pothos/core": "^3.41.1", "@pothos/plugin-simple-objects": "^3.7.0", "@tsconfig/node18": "^18.2.4", "@tsconfig/strictest": "^2.0.5", "@types/body-parser": "^1.19.5", "@types/express": "^4.17.21", "@types/json-bigint": "^1.0.4", "@types/node": "^20.14.7", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "apollo-server-express": "^3.13.0", "body-parser": "^1.20.2", "doctoc": "^2.2.1", "dripip": "^0.10.0", "eslint": "^9.5.0", "eslint-config-prisma": "^0.6.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-simple-import-sort": "^12.1.0", "eslint-plugin-tsdoc": "^0.3.0", "eslint-typescript": "^1.1.0", "express": "^4.19.2", "get-port": "^7.1.0", "graphql": "^16.9.0", "graphql-scalars": "^1.23.0", "graphql-tag": "^2.12.6", "jsdom": "^24.1.0", "json-bigint": "^1.0.0", "publint": "^0.2.8", "tsx": "^4.15.7", "type-fest": "^4.20.1", "typescript": "^5.5.2", "typescript-eslint": "^7.13.1", "vitest": "^1.6.0"}, "scripts": {"gen:test:schema": "tsx tests/_/schemaGenerate.ts", "demo": "tsx src/cli/generateSchema.ts && dprint fmt src/demo.ts", "dev": "rm -rf dist && tsc --watch", "format": "pnpm build:docs && dprint fmt", "lint": "eslint . --fix", "check": "pnpm check:types && pnpm check:format && pnpm check:lint && pnpm check:publint", "check:types": "pnpm tsc --noEmit", "check:format": "dprint check && pnpm build:docs && git diff --exit-code README.md", "check:lint": "eslint . --max-warnings 0", "check:publint": "publint run --strict", "build:docs": "doctoc README.md --notitle && dprint fmt README.md", "build": "pnpm clean && pnpm tsc --project tsconfig.build.json", "clean": "tsc --build --clean && rm -rf build", "test": "vitest", "test:web": "vitest --environment jsdom", "test:types": "vitest --typecheck", "test:coverage": "pnpm test -- --coverage", "release:stable": "dripip stable", "release:preview": "dripip preview", "release:pr": "dripip pr"}}