import { GraphQLClient } from 'graphql-request'

// Hygraph (GraphCMS) configuration
const HYGRAPH_ENDPOINT = process.env.NEXT_PUBLIC_HYGRAPH_ENDPOINT || 'https://your-hygraph-endpoint.hygraph.com/v2/your-project-id/master'
const HYGRAPH_TOKEN = process.env.HYGRAPH_TOKEN

// Create GraphQL client
export const hygraphClient = new GraphQLClient(HYGRAPH_ENDPOINT, {
  headers: {
    ...(HYGRAPH_TOKEN && { Authorization: `Bearer ${HYGRAPH_TOKEN}` }),
  },
})

// GraphQL queries
export const GET_ALL_BLOG_POSTS = `
  query GetAllBlogPosts {
    blogPosts(orderBy: publishedAt_DESC) {
      id
      title
      excerpt
      slug
      publishedAt
      featured
      coverImage {
        url
        width
        height
        alt
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
          width
          height
          alt
        }
        socialLinks
      }
      content {
        html
        text
      }
    }
  }
`

export const GET_BLOG_POST_BY_SLUG = `
  query GetBlogPostBySlug($slug: String!) {
    blogPost(where: { slug: $slug }) {
      id
      title
      excerpt
      slug
      publishedAt
      featured
      coverImage {
        url
        width
        height
        alt
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
          width
          height
          alt
        }
        socialLinks
      }
      content {
        html
        text
      }
      seo {
        title
        description
        keywords
      }
    }
  }
`

export const GET_FEATURED_POSTS = `
  query GetFeaturedPosts {
    blogPosts(where: { featured: true }, orderBy: publishedAt_DESC, first: 3) {
      id
      title
      excerpt
      slug
      publishedAt
      featured
      coverImage {
        url
        width
        height
        alt
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
          width
          height
          alt
        }
        socialLinks
      }
    }
  }
`

export const GET_POSTS_BY_CATEGORY = `
  query GetPostsByCategory($categorySlug: String!) {
    blogPosts(where: { category: { slug: $categorySlug } }, orderBy: publishedAt_DESC) {
      id
      title
      excerpt
      slug
      publishedAt
      featured
      coverImage {
        url
        width
        height
        alt
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
          width
          height
          alt
        }
        socialLinks
      }
    }
  }
`

export const GET_POSTS_BY_TAG = `
  query GetPostsByTag($tagSlug: String!) {
    blogPosts(where: { tags_some: { slug: $tagSlug } }, orderBy: publishedAt_DESC) {
      id
      title
      excerpt
      slug
      publishedAt
      featured
      coverImage {
        url
        width
        height
        alt
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
          width
          height
          alt
        }
        socialLinks
      }
    }
  }
`

export const GET_CATEGORIES = `
  query GetCategories {
    categories(orderBy: name_ASC) {
      id
      name
      slug
      description
    }
  }
`

export const GET_TAGS = `
  query GetTags {
    tags(orderBy: name_ASC) {
      id
      name
      slug
    }
  }
`
