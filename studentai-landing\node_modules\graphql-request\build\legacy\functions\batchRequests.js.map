{"version": 3, "file": "batchRequests.js", "sourceRoot": "", "sources": ["../../../src/legacy/functions/batchRequests.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAA;AAkB3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,CAAC,MAAM,aAAa,GAAkB,KAAK,EAAE,GAAG,IAAuB,EAAE,EAAE;IAC/E,MAAM,MAAM,GAAG,8BAA8B,CAAC,IAAI,CAAC,CAAA;IACnD,MAAM,MAAM,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IAC5C,OAAO,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;AACrC,CAAC,CAAA;AAMD,MAAM,CAAC,MAAM,8BAA8B,GAAG,CAAC,IAAuB,EAAgC,EAAE;IACtG,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;IAChB,CAAC;SAAM,CAAC;QACN,OAAO;YACL,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YACZ,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;YAClB,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;YACvB,MAAM,EAAE,SAAS;SAClB,CAAA;IACH,CAAC;AACH,CAAC,CAAA;AAcD,MAAM,CAAC,MAAM,qBAAqB,GAAG,CACnC,kBAAuE,EACvE,cAA4B,EACH,EAAE;IAC3B,2BAA2B;IAC3B,OAAQ,kBAA8C,CAAC,SAAS;QAC9D,CAAC,CAAE,kBAA8C;QACjD,CAAC,CAAC;YACA,SAAS,EAAE,kBAA+C;YAC1D,cAAc,EAAE,cAAc;YAC9B,MAAM,EAAE,SAAS;SAClB,CAAA;AACL,CAAC,CAAA"}