{"version": 3, "file": "analyzeFunction.js", "sourceRoot": "", "sources": ["../../src/lib/analyzeFunction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAA;AAI7C,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,EAAoC,EAAE,EAAE;IACtE,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,MAAM,CAAA;IAC3D,IAAI,CAAC,MAAM;QAAE,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;IAEvE,MAAM,IAAI,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAA;IAChE,IAAI,IAAI,KAAK,SAAS;QAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;IAEhF,MAAM,UAAU,GAAgB,EAAE,CAAA;IAElC,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC,CAAA;QAC7E,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC5C,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAA;YAEvG,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,cAAc;oBACjB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAO,CAAC,cAAc,CAAE,CAAA;oBAChD,MAAM,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;wBACrE,MAAM,IAAI,GAAG,MAAM,CAAC,MAAO,CAAC,MAAM,CAAC,CAAA;wBACnC,IAAI,IAAI,KAAK,SAAS;4BAAE,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAA;wBAC9F,OAAO,IAAI,CAAA;oBACb,CAAC,CAAC,CAAA;oBACF,OAAO;wBACL,IAAI;wBACJ,KAAK;qBACG,CAAA;gBACZ,KAAK,MAAM;oBACT,OAAO;wBACL,IAAI;wBACJ,KAAK,EAAE,MAAM,CAAC,MAAO,CAAC,MAAM,CAAE;qBACtB,CAAA;gBACZ,KAAK,IAAI;oBACP,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;gBAC3D;oBACE,MAAM,cAAc,CAAC,IAAI,CAAC,CAAA;YAC9B,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,UAAU,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAA;IACtC,CAAC;IAED,OAAO;QACL,IAAI;QACJ,UAAU;KACX,CAAA;AACH,CAAC,CAAA;AAED;;GAEG;AACH,kGAAkG;AAElG;;GAEG;AACH,MAAM,eAAe,GACnB,uKAAuK,CAAA;AAEzK;;GAEG;AACH,MAAM,yBAAyB,GAAG,wDAAwD,CAAA;AAE1F;;GAEG;AACH,MAAM,mBAAmB,GAAG,yCAAyC,CAAA"}