// Hygraph (GraphCMS) TypeScript types

export interface HygraphAsset {
  url: string
  width?: number
  height?: number
  alt?: string
}

export interface HygraphCategory {
  id: string
  name: string
  slug: string
  description?: string
}

export interface HygraphTag {
  id: string
  name: string
  slug: string
}

export interface HygraphAuthor {
  id: string
  name: string
  title?: string
  bio?: string
  avatar?: HygraphAsset
  socialLinks?: {
    twitter?: string
    linkedin?: string
    website?: string
  }
}

export interface HygraphContent {
  html: string
  text: string
}

export interface HygraphSEO {
  title?: string
  description?: string
  keywords?: string[]
}

export interface HygraphBlogPost {
  id: string
  title: string
  excerpt: string
  slug: string
  publishedAt: string
  featured?: boolean
  coverImage?: HygraphAsset
  category: HygraphCategory
  tags: HygraphTag[]
  author: HygraphAuthor
  content: HygraphContent
  seo?: HygraphSEO
}

// Response types for GraphQL queries
export interface GetAllBlogPostsResponse {
  blogPosts: HygraphBlogPost[]
}

export interface GetBlogPostBySlugResponse {
  blogPost: HygraphBlogPost | null
}

export interface GetFeaturedPostsResponse {
  blogPosts: HygraphBlogPost[]
}

export interface GetPostsByCategoryResponse {
  blogPosts: HygraphBlogPost[]
}

export interface GetPostsByTagResponse {
  blogPosts: HygraphBlogPost[]
}

export interface GetCategoriesResponse {
  categories: HygraphCategory[]
}

export interface GetTagsResponse {
  tags: HygraphTag[]
}

// Converted types for compatibility with existing blog interface
export interface Author {
  name: string
  title?: string
  avatar?: string
  bio?: string
  socialLinks?: {
    twitter?: string
    linkedin?: string
    website?: string
  }
}

export interface BlogPost {
  id: string
  title: string
  excerpt: string
  slug: string
  date: string
  author: Author
  content: string
  coverImage?: string
  category: string
  tags: string[]
  featured?: boolean
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
}

// Utility functions to convert Hygraph data to existing blog format
export function convertHygraphPostToBlogPost(hygraphPost: HygraphBlogPost): BlogPost {
  return {
    id: hygraphPost.id,
    title: hygraphPost.title,
    excerpt: hygraphPost.excerpt,
    slug: hygraphPost.slug,
    date: hygraphPost.publishedAt,
    author: {
      name: hygraphPost.author.name,
      title: hygraphPost.author.title,
      avatar: hygraphPost.author.avatar?.url,
      bio: hygraphPost.author.bio,
      socialLinks: hygraphPost.author.socialLinks,
    },
    content: hygraphPost.content.html,
    coverImage: hygraphPost.coverImage?.url,
    category: hygraphPost.category.name,
    tags: hygraphPost.tags.map(tag => tag.name),
    featured: hygraphPost.featured,
    seo: hygraphPost.seo,
  }
}

export function convertHygraphPostsToBlogPosts(hygraphPosts: HygraphBlogPost[]): BlogPost[] {
  return hygraphPosts.map(convertHygraphPostToBlogPost)
}
