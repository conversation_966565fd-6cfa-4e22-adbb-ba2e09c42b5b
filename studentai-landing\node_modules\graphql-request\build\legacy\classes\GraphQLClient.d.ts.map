{"version": 3, "file": "GraphQLClient.d.ts", "sourceRoot": "", "sources": ["../../../src/legacy/classes/GraphQLClient.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAA;AAE1E,OAAO,KAAK,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAA;AAM5G,OAAO,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,8BAA8B,EAAE,MAAM,qBAAqB,CAAA;AAC1G,OAAO,EACL,KAAK,qBAAqB,EAC1B,KAAK,iBAAiB,EACtB,KAAK,aAAa,EAClB,KAAK,SAAS,EACf,MAAM,qBAAqB,CAAA;AAE5B;;GAEG;AACH,qBAAa,aAAa;IAEtB,OAAO,CAAC,GAAG;aACK,aAAa,EAAE,aAAa;gBADpC,GAAG,EAAE,MAAM,EACH,aAAa,GAAE,aAAkB;IAGnD;;OAEG;IACH,UAAU,EAAE,gBAAgB,CA6D3B;IAED;;OAEG;IAEG,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,QAAQ,EAAE,eAAe,GAAG,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,0BAA0B,EAAE,8BAA8B,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAC9K,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAkE5F;;OAEG;IACG,aAAa,CACjB,YAAY,SAAS,WAAW,EAChC,UAAU,SAAS,SAAS,GAAG,SAAS,EAExC,SAAS,EAAE,oBAAoB,CAAC,UAAU,CAAC,EAAE,EAC7C,cAAc,CAAC,EAAE,WAAW,GAC3B,OAAO,CAAC,YAAY,CAAC;IAClB,aAAa,CACjB,YAAY,SAAS,WAAW,EAChC,UAAU,SAAS,SAAS,GAAG,SAAS,EACxC,OAAO,EAAE,oBAAoB,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC;IA+DnE,UAAU,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI;IAKtC;;OAEG;IACH,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IAc3C;;OAEG;IACH,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;CAIjC;AAED,UAAU,gBAAgB;IACxB,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EACjC,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,CAAC,EACb,cAAc,CAAC,EAAE,WAAW,GAC3B,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAA;IACpC,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC,GAAG,OAAO,CAC1E,qBAAqB,CAAC,CAAC,CAAC,CACzB,CAAA;CACF"}