import { ClientError } from '../legacy/classes/ClientError.js';
import { request } from '../legacy/functions/request.js';
export { GraphQLClient } from '../legacy/classes/GraphQLClient.js';
export { batchRequests } from '../legacy/functions/batchRequests.js';
export { gql } from '../legacy/functions/gql.js';
export { rawRequest } from '../legacy/functions/rawRequest.js';
export { analyzeDocument } from '../legacy/helpers/analyzeDocument.js';
export { ClientError, request, };
export default request;
//# sourceMappingURL=main.js.map