# Hygraph (GraphCMS) Integration Setup

This project uses Hygraph (formerly GraphCMS) as a headless CMS for managing blog content. Follow these steps to set up your Hygraph project and integrate it with the application.

## 🚀 Quick Start

The integration is already complete! You just need to:
1. Set up your Hygraph project (follow steps below)
2. Add environment variables to your `.env.local` file
3. Your blog will automatically fetch data from Hygraph

## 1. Create a Hygraph Account

1. Go to [Hygraph.com](https://hygraph.com) and create a free account
2. Create a new project
3. Choose a region closest to your users

## 2. Set Up Content Models

Create the following content models in your Hygraph project:

### Author Model
- **Model Name**: `Author`
- **API ID**: `author`
- **Fields**:
  - `name` (Single line text, Required)
  - `title` (Single line text)
  - `bio` (Multi-line text)
  - `avatar` (Asset - Single asset)
  - `socialLinks` (JSON)

### Category Model
- **Model Name**: `Category`
- **API ID**: `category`
- **Fields**:
  - `name` (Single line text, Required)
  - `slug` (Slug, Required, based on name)
  - `description` (Multi-line text)

### Tag Model
- **Model Name**: `Tag`
- **API ID**: `tag`
- **Fields**:
  - `name` (Single line text, Required)
  - `slug` (Slug, Required, based on name)

### SEO Model
- **Model Name**: `SEO`
- **API ID**: `seo`
- **Fields**:
  - `title` (Single line text)
  - `description` (Multi-line text)
  - `keywords` (Single line text, Multiple values)

### Blog Post Model
- **Model Name**: `BlogPost`
- **API ID**: `blogPost`
- **Fields**:
  - `title` (Single line text, Required)
  - `slug` (Slug, Required, based on title)
  - `excerpt` (Multi-line text, Required)
  - `content` (Rich Text, Required)
  - `coverImage` (Asset - Single asset)
  - `publishedAt` (Date and time, Required)
  - `featured` (Boolean)
  - `author` (Reference - Single reference to Author)
  - `category` (Reference - Single reference to Category)
  - `tags` (Reference - Multiple references to Tag)
  - `seo` (Reference - Single reference to SEO)

## 3. Configure API Access

1. Go to **Settings** > **API Access**
2. Create a new **Content API** token with read permissions
3. Note down your **Content API Endpoint** and **Token**

## 4. Environment Variables

Create a `.env.local` file in your project root and add:

```env
NEXT_PUBLIC_HYGRAPH_ENDPOINT=https://your-region.hygraph.com/v2/your-project-id/master
HYGRAPH_TOKEN=your-hygraph-content-api-token
```

Replace the values with your actual Hygraph endpoint and token.

## 5. Content Creation

1. Create some authors, categories, and tags first
2. Create blog posts and link them to authors, categories, and tags
3. Make sure to publish your content (click the publish button)

## 6. Testing

1. Start your development server: `npm run dev`
2. Test the Hygraph connection: Visit `http://localhost:3000/api/test-hygraph`
   - If successful: You'll see your blog posts data
   - If failed: You'll see error details and fallback message
3. Navigate to `/blog` to see your Hygraph content
4. If Hygraph is not configured or fails, the app will fall back to static content

## 7. Environment Variables

Add these to your `.env.local` file:

```env
# Hygraph Configuration
NEXT_PUBLIC_HYGRAPH_ENDPOINT=https://your-region.hygraph.com/v2/your-project-id/master
HYGRAPH_TOKEN=your-hygraph-content-api-token
```

**How to get these values:**
1. **NEXT_PUBLIC_HYGRAPH_ENDPOINT**: Go to your Hygraph project → Settings → API Access → Content API → Copy the endpoint URL
2. **HYGRAPH_TOKEN**: In the same section, create a new token with "Read" permissions and copy it

## GraphQL Queries

The application uses the following GraphQL queries:

- `GET_ALL_BLOG_POSTS` - Fetches all published blog posts
- `GET_BLOG_POST_BY_SLUG` - Fetches a single blog post by slug
- `GET_FEATURED_POSTS` - Fetches featured blog posts
- `GET_POSTS_BY_CATEGORY` - Fetches posts by category
- `GET_POSTS_BY_TAG` - Fetches posts by tag
- `GET_CATEGORIES` - Fetches all categories
- `GET_TAGS` - Fetches all tags

## Fallback Behavior

If Hygraph is not configured or fails to respond, the application will:
1. Log errors to the console
2. Fall back to static blog content defined in `lib/blog.ts`
3. Continue to function normally with the fallback data

This ensures your application remains functional even if there are issues with the CMS.

## Content Management Workflow

1. **Content Creation**: Use Hygraph's web interface to create and edit blog posts
2. **Preview**: Use Hygraph's preview features to review content before publishing
3. **Publishing**: Publish content in Hygraph to make it available via the API
4. **Automatic Updates**: Your Next.js application will automatically fetch the latest content

## Performance Considerations

- The application caches GraphQL responses
- Consider implementing ISR (Incremental Static Regeneration) for better performance
- Use Hygraph's CDN for asset delivery
- Consider implementing pagination for large numbers of blog posts

## Troubleshooting

1. **No content showing**: Check your environment variables and API permissions
2. **GraphQL errors**: Verify your content models match the expected schema
3. **Images not loading**: Ensure assets are published and accessible
4. **Slow loading**: Consider implementing caching strategies

For more information, visit the [Hygraph Documentation](https://hygraph.com/docs).
