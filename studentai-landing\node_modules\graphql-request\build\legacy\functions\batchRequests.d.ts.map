{"version": 3, "file": "batchRequests.d.ts", "sourceRoot": "", "sources": ["../../../src/legacy/functions/batchRequests.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAA;AAErE,MAAM,MAAM,oBAAoB,CAAC,CAAC,SAAS,SAAS,GAAG,SAAS,IAAI;IAClE,QAAQ,EAAE,eAAe,CAAA;IACzB,SAAS,CAAC,EAAE,CAAC,CAAA;CACd,CAAA;AAED,MAAM,WAAW,oBAAoB,CAAC,CAAC,SAAS,SAAS,GAAG,SAAS;IACnE,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAA;IACpC,cAAc,CAAC,EAAE,WAAW,CAAA;IAC5B,MAAM,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAA;CAC/B;AAED,MAAM,WAAW,4BAA4B,CAAC,CAAC,SAAS,SAAS,GAAG,SAAS,CAAE,SAAQ,oBAAoB,CAAC,CAAC,CAAC;IAC5G,GAAG,EAAE,MAAM,CAAA;CACZ;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,eAAO,MAAM,aAAa,EAAE,aAI3B,CAAA;AAED,KAAK,iBAAiB,GAClB,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,oBAAoB,EAAE,EAAE,cAAc,CAAC,EAAE,WAAW,CAAC,GAC9E,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAA;AAE3C,eAAO,MAAM,8BAA8B,SAAU,iBAAiB,KAAG,4BAWxE,CAAA;AAGD,UAAU,aAAa;IACrB,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;IACrJ,CAAC,CAAC,SAAS,WAAW,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,OAAO,EAAE,4BAA4B,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;CAC/G;AAED,MAAM,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,CAAA;AAE/C,UAAU,MAAM,CAAC,IAAI,SAAS,MAAM,GAAG,MAAM;IAC3C,IAAI,EAAE,IAAI,CAAA;CACX;AAED,eAAO,MAAM,qBAAqB,GAAI,CAAC,SAAS,SAAS,+BACnC,oBAAoB,CAAC,CAAC,CAAC,EAAE,GAAG,oBAAoB,CAAC,CAAC,CAAC,mBACtD,WAAW,KAC3B,oBAAoB,CAAC,CAAC,CASxB,CAAA"}