{"version": 3, "file": "GraphQLClient.js", "sourceRoot": "", "sources": ["../../../src/legacy/classes/GraphQLClient.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAA;AAE/E,OAAO,EAAE,qBAAqB,EAAE,MAAM,+BAA+B,CAAA;AACrE,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAA;AAChE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAA;AAC1D,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAA;AAC/D,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAA;AASrD;;GAEG;AACH,MAAM,OAAO,aAAa;IAEd;IACQ;IAFlB,YACU,GAAW,EACH,gBAA+B,EAAE;QADzC,QAAG,GAAH,GAAG,CAAQ;QACH,kBAAa,GAAb,aAAa,CAAoB;IAChD,CAAC;IAEJ;;OAEG;IACH,UAAU,GAAqB,KAAK,EAIlC,GAAG,IAAsC,EACN,EAAE;QACrC,MAAM,CAAC,cAAc,EAAE,SAAS,EAAE,cAAc,CAAC,GAAG,IAAI,CAAA;QACxD,MAAM,iBAAiB,GAAG,mBAAmB,CAC3C,cAAc,EACd,SAAS,EACT,cAAc,CACf,CAAA;QACD,MAAM,EACJ,OAAO,EACP,KAAK,GAAG,UAAU,CAAC,KAAK,EACxB,MAAM,GAAG,MAAM,EACf,iBAAiB,EACjB,kBAAkB,EAClB,oBAAoB,EACpB,GAAG,YAAY,EAChB,GAAG,IAAI,CAAC,aAAa,CAAA;QACtB,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QACpB,IAAI,iBAAiB,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC3C,YAAY,CAAC,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAA;QAChD,CAAC;QAED,MAAM,QAAQ,GAAG,eAAe,CAC9B,iBAAiB,CAAC,KAAK,EACvB,oBAAoB,CACrB,CAAA;QAED,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC;YAChC,GAAG;YACH,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,QAAQ;gBACR,SAAS,EAAE,iBAAiB,CAAC,SAAS;aACvC;YACD,OAAO,EAAE;gBACP,GAAG,wBAAwB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACpD,GAAG,wBAAwB,CAAC,iBAAiB,CAAC,cAAc,CAAC;aAC9D;YACD,KAAK;YACL,MAAM;YACN,YAAY;YACZ,UAAU,EAAE,iBAAiB;SAC9B,CAAC,CAAA;QAEF,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,kBAAkB,CAAC,QAAQ,EAAE;gBACjC,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,SAAS;gBACT,GAAG,EAAE,IAAI,CAAC,GAAG;aACd,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,QAAQ,YAAY,KAAK,EAAE,CAAC;YAC9B,MAAM,QAAQ,CAAA;QAChB,CAAC;QAED,OAAO,QAAQ,CAAA;IACjB,CAAC,CAAA;IAQD,KAAK,CAAC,OAAO,CACX,iBAGqB,EACrB,GAAG,0BAA6D;QAEhE,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,GAAG,0BAA0B,CAAA;QAC9D,MAAM,cAAc,GAAG,gBAAgB,CACrC,iBAAiB,EACjB,SAAS,EACT,cAAc,CACf,CAAA;QAED,MAAM,EACJ,OAAO,EACP,KAAK,GAAG,UAAU,CAAC,KAAK,EACxB,MAAM,GAAG,MAAM,EACf,iBAAiB,EACjB,kBAAkB,EAClB,oBAAoB,EACpB,GAAG,YAAY,EAChB,GAAG,IAAI,CAAC,aAAa,CAAA;QACtB,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QACpB,IAAI,cAAc,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACxC,YAAY,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAA;QAC7C,CAAC;QAED,MAAM,gBAAgB,GAAG,eAAe,CACtC,cAAc,CAAC,QAAQ,EACvB,oBAAoB,CACrB,CAAA;QAED,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC;YAChC,GAAG;YACH,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,gBAAgB;gBAC1B,SAAS,EAAE,cAAc,CAAC,SAAS;aACpC;YACD,OAAO,EAAE;gBACP,GAAG,wBAAwB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACpD,GAAG,wBAAwB,CAAC,cAAc,CAAC,cAAc,CAAC;aAC3D;YACD,KAAK;YACL,MAAM;YACN,YAAY;YACZ,UAAU,EAAE,iBAAiB;SAC9B,CAAC,CAAA;QAEF,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,kBAAkB,CAAC,QAAQ,EAAE;gBACjC,aAAa,EAAE,gBAAgB,CAAC,aAAa;gBAC7C,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,GAAG,EAAE,IAAI,CAAC,GAAG;aACd,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,QAAQ,YAAY,KAAK,EAAE,CAAC;YAC9B,MAAM,QAAQ,CAAA;QAChB,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAA;IACtB,CAAC;IAgBD,KAAK,CAAC,aAAa,CAIjB,kBAEoC,EACpC,cAA4B;QAE5B,MAAM,mBAAmB,GAAG,qBAAqB,CAC/C,kBAAkB,EAClB,cAAc,CACf,CAAA;QACD,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,GAAG,YAAY,EAAE,GAAG,IAAI,CAAC,aAAa,CAAA;QAE7E,IAAI,mBAAmB,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC7C,YAAY,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAA;QAClD,CAAC;QAED,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,SAAS,CAAC,GAAG,CACzD,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,eAAe,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAClE,CAAA;QACD,MAAM,WAAW,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QACzE,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC3E,MAAM,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC,GAAG,CACjD,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAC7B,CAAA;QAED,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC;YAChC,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE;gBACP,IAAI,EAAE,OAAO;gBACb,aAAa,EAAE,SAAS;gBACxB,KAAK,EAAE,WAAW;gBAClB,YAAY;gBACZ,SAAS;aACV;YACD,OAAO,EAAE;gBACP,GAAG,wBAAwB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACpD,GAAG,wBAAwB,CAAC,mBAAmB,CAAC,cAAc,CAAC;aAChE;YACD,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK;YACnD,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,MAAM;YAC3C,YAAY;YACZ,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB;SACjD,CAAC,CAAA;QAEF,IAAI,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;YAC1C,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,QAAQ,EAAE;gBACpD,aAAa,EAAE,SAAS;gBACxB,SAAS;gBACT,GAAG,EAAE,IAAI,CAAC,GAAG;aACd,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,QAAQ,YAAY,KAAK,EAAE,CAAC;YAC9B,MAAM,QAAQ,CAAA;QAChB,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAA;IACtB,CAAC;IAED,UAAU,CAAC,OAAoB;QAC7B,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAA;QACpC,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,GAAW,EAAE,KAAa;QAClC,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAA;QAEtC,IAAI,OAAO,EAAE,CAAC;YACZ,oDAAoD;YACpD,wBAAwB;YACxB,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;QACtB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAA;QAC/C,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,KAAa;QACvB,IAAI,CAAC,GAAG,GAAG,KAAK,CAAA;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;CACF"}