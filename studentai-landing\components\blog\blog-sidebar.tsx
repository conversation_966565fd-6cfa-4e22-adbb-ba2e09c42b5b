import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import type { BlogPost } from "@/lib/blog"
import { Calendar } from "lucide-react"
import Link from "next/link"

interface BlogSidebarProps {
  recentPosts?: BlogPost[]
  categories?: string[]
  popularTags?: string[]
}

export function BlogSidebar({ recentPosts = [], categories = [], popularTags = [] }: BlogSidebarProps) {
  return (
    <div className="space-y-8">
      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input placeholder="Search articles..." className="flex-grow" />
            <Button>Search</Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Posts */}
      {recentPosts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Posts</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentPosts.map((post) => (
              <div key={post.id} className="border-b pb-4 last:border-0 last:pb-0">
                <Link href={`/blog/${post.slug || post.id}`} className="group">
                  <h3 className="font-medium group-hover:text-primary transition-colors line-clamp-2">{post.title}</h3>
                </Link>
                <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                  <Calendar className="h-3 w-3" />
                  <time dateTime={post.date}>
                    {new Date(post.date).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "short",
                      day: "numeric",
                    })}
                  </time>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Categories */}
      {categories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {categories.map((category) => (
                <div key={category}>
                  <Link
                    href={`/blog/category/${category.toLowerCase().replace(/ /g, "-")}`}
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    {category}
                  </Link>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Popular Tags */}
      {popularTags.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Popular Tags</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {popularTags.map((tag) => (
                <Link href={`/blog/tag/${tag.replace(/ /g, "-")}`} key={tag}>
                  <Badge variant="secondary" className="hover:bg-secondary/80 cursor-pointer">
                    {tag}
                  </Badge>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Newsletter */}
      <Card className="bg-primary text-primary-foreground">
        <CardHeader>
          <CardTitle>Subscribe to Our Newsletter</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">Get the latest articles and resources straight to your inbox.</p>
          <div className="space-y-2">
            <Input
              placeholder="Your email address"
              className="bg-primary-foreground/10 border-primary-foreground/20 text-primary-foreground placeholder:text-primary-foreground/70"
            />
            <Button variant="secondary" className="w-full">
              Subscribe
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
