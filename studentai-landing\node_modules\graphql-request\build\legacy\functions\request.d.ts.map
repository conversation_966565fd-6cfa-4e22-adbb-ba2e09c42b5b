{"version": 3, "file": "request.d.ts", "sourceRoot": "", "sources": ["../../../src/legacy/functions/request.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAA;AAE1E,OAAO,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,EAAE,8BAA8B,EAAE,MAAM,qBAAqB,CAAA;AAErH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AAKH,wBAAsB,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;AAEpH,wBAAsB,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,GAAG,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,0BAA0B,EAAE,8BAA8B,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;AAWjN,eAAO,MAAM,gBAAgB,GAAI,CAAC,SAAS,SAAS,8BAC/B,eAAe,GAAG,cAAc,CAAC,CAAC,CAAC,cAC1C,CAAC,mBACI,WAAW,KAC3B,cAAc,CAAC,CAAC,CASlB,CAAA;AAED,MAAM,MAAM,sBAAsB,CAAC,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,CAAC,GAAG,OAAO,IAAI;IACjF,GAAG,EAAE,MAAM,CAAA;CACZ,GAAG,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAExB,eAAO,MAAM,wBAAwB,GAAI,CAAC,SAAS,SAAS,yBAC5C,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC,aACrC,eAAe,iCACK,8BAA8B,CAAC,CAAC,CAAC,KAC/D,sBAAsB,CAAC,CAAC,CAW1B,CAAA"}