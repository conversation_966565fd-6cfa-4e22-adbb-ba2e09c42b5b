import { GraphQLError } from '../error/GraphQLError';
/**
 * Upholds the spec rules about naming.
 * @deprecated Please use `assertName` instead. Will be removed in v17
 */
export declare function assertValidName(name: string): string;
/**
 * Returns an Error if a name is invalid.
 * @deprecated Please use `assertName` instead. Will be removed in v17
 */
export declare function isValidNameError(
  name: string,
): GraphQLError | undefined;
