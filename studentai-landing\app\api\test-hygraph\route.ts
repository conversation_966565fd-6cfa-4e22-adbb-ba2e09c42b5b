import { NextResponse } from 'next/server'
import { hygraphClient, GET_ALL_BLOG_POSTS } from '@/lib/hygraph'
import { GetAllBlogPostsResponse } from '@/lib/hygraph-types'

export async function GET() {
  try {
    // Test the Hygraph connection
    const data = await hygraphClient.request<GetAllBlogPostsResponse>(GET_ALL_BLOG_POSTS)
    
    return NextResponse.json({
      success: true,
      message: 'Hygraph connection successful',
      postsCount: data.blogPosts.length,
      posts: data.blogPosts.map(post => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        publishedAt: post.publishedAt
      }))
    })
  } catch (error) {
    console.error('Hygraph connection error:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Hygraph connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      fallback: 'Using static blog data as fallback'
    }, { status: 500 })
  }
}
