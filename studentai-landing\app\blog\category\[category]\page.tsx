import { PostCard } from "@/components/blog/post-card"
import { Footer } from "@/components/footer"
import { Navbar } from "@/components/navbar"
import { Button } from "@/components/ui/button"
import { blogCategories, getPostsByCategory } from "@/lib/blog"
import { ArrowLeft } from "lucide-react"
import type { Metadata } from "next"
import Link from "next/link"
import { notFound } from "next/navigation"

interface CategoryPageProps {
  params: {
    category: string
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const category = params.category.replace(/-/g, " ")
  const formattedCategory = category
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")

  return {
    title: `${formattedCategory} Articles | StudentAidDetector Blog`,
    description: `Browse our collection of articles and resources about ${formattedCategory.toLowerCase()}.`,
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const categorySlug = params.category
  const category = categorySlug.replace(/-/g, " ")

  // Format category name for display (capitalize each word)
  const formattedCategory = category
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")

  // Check if category exists
  if (!blogCategories.map((c) => c.toLowerCase()).includes(category.toLowerCase())) {
    notFound()
  }

  const posts = await getPostsByCategory(categorySlug)

  if (posts.length === 0) {
    notFound()
  }

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-12 flex-grow">
        <Link href="/blog" className="inline-block mb-8">
          <Button variant="ghost" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to all posts
          </Button>
        </Link>

        <header className="mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">{formattedCategory}</h1>
          <p className="text-xl text-muted-foreground">
            Browse our collection of articles and resources about {category.toLowerCase()}
          </p>
        </header>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {posts.map((post) => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      </div>

      <Footer />
    </main>
  )
}
